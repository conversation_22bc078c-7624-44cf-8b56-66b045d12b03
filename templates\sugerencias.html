<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sugerencias - VerdeQR</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/gestion.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/common_header.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/notifications.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/menu_buttons.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="gestion-body">
    {% include 'flash_messages.html' %}
    <header class="header-principal">
        <div class="logo-container">
            <img src="{{ url_for('static', filename='css/js/img/logo.png') }}" alt="Logo VerdeQR" class="logo" style="border-radius: 50%; width: 60px; height: 60px; object-fit: cover;">
        </div>
        <nav class="menu-principal">
            <ul id="menu">
                <li><a href="{{ url_for('gestion') }}"><i class="fas fa-home"></i><span>Inicio</span></a></li>
                <li><a href="{{ url_for('arbol') }}"><i class="fas fa-tree"></i><span>Árboles</span></a></li>
                <li><a href="{{ url_for('centro') }}"><i class="fas fa-university"></i><span>Centros</span></a></li>
                <li><a href="{{ url_for('especie') }}"><i class="fas fa-leaf"></i><span>Especies</span></a></li>
                <li><a href="{{ url_for('uso_arbol') }}"><i class="fas fa-tags"></i><span>Usos</span></a></li>
                <li><a href="{{ url_for('tipo_bosque') }}"><i class="fas fa-mountain"></i><span>Tipos de Bosque</span></a></li>
                <li><a href="{{ url_for('curiosidades') }}"><i class="fas fa-lightbulb"></i><span>Curiosidades</span></a></li>
                <li><a href="{{ url_for('interacciones') }}"><i class="fas fa-network-wired"></i><span>Interacciones Ecológicas</span></a></li>
                <li><a href="{{ url_for('qr') }}"><i class="fas fa-qrcode"></i><span>QR</span></a></li>
                <li><a href="{{ url_for('sugerencias') }}"><i class="fas fa-comments"></i><span>Sugerencias</span></a></li>
                <li><a href="{{ url_for('gestion_usuarios') }}"><i class="fas fa-users"></i><span>Usuarios</span></a></li>
            </ul>
        </nav>
        <div class="user-section">
            <a href="{{ url_for('perfil') }}" class="user-profile-link">
                {% if session['usuario'].get('Imagen') %}
                    <img src="{{ url_for('static', filename=session['usuario']['Imagen']) }}" alt="Usuario" class="user-avatar">
                {% else %}
                    {% if determinar_genero(session['usuario']['Nombres']) == 'femenino' %}
                        <img src="{{ url_for('static', filename='css/js/img/avatarf.jpg') }}" alt="Usuario" class="user-avatar">
                    {% else %}
                        <img src="{{ url_for('static', filename='css/js/img/avatarm.jpg') }}" alt="Usuario" class="user-avatar">
                    {% endif %}
                {% endif %}
                <div class="user-info">
                    <span class="user-name">{{ session['usuario']['Nombres'] }} {{ session['usuario']['Apellidos'] }}</span>
                    <span class="user-email">{{ session['usuario']['Correo'] }}</span>
                </div>
            </a>
        </div>
    </header>

    <main class="container">
        <!-- Formulario para registrar sugerencias -->
        <div class="contenedor-seccion">
            <h2>Registrar Sugerencia</h2>
            <form method="POST" action="{{ url_for('registrar_sugerencia') }}" class="formulario-gestion">
                <div class="form-group">
                    <label for="nombre">Nombre:</label>
                    <input type="text" id="nombre" name="nombre" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="email">Email:</label>
                    <input type="email" id="email" name="email" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="sugerencia">Sugerencia:</label>
                    <textarea id="sugerencia" name="sugerencia" class="form-control" rows="4" required></textarea>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Registrar Sugerencia
                </button>
            </form>
        </div>

        <!-- Lista de sugerencias -->
        <div class="contenedor-seccion sugerencias-container">
            <h2>Gestión de Sugerencias</h2>

            <div class="table-container">
                <table class="table-gestion">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nombre</th>
                            <th>Email</th>
                            <th>Sugerencia</th>
                            <th>Fecha</th>
                            <th>Estado</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for sugerencia in sugerencias %}
                        <tr>
                            <td>{{ sugerencia.IDSugerencia }}</td>
                            <td>{{ sugerencia.Nombre }}</td>
                            <td>{{ sugerencia.Email }}</td>
                            <td>{{ sugerencia.Sugerencia }}</td>
                            <td>{{ sugerencia.Fecha }}</td>
                            <td>
                                <span class="badge {% if sugerencia.Estado == 1 %}badge-success{% else %}badge-secondary{% endif %}">
                                    {{ sugerencia.EstadoNombre }}
                                </span>
                            </td>
                            <td class="acciones-cell">
                                <div class="acciones-botones">
                                    <a href="{{ url_for('eliminar_sugerencia', id=sugerencia.IDSugerencia) }}" class="btn btn-danger btn-sm" onclick="return confirm('¿Estás seguro de que deseas eliminar esta sugerencia?')">
                                        <i class="fas fa-trash"></i> Eliminar
                                    </a>
                                    <select class="form-select estado-sugerencia"
                                            data-id="{{ sugerencia.IDSugerencia }}"
                                            aria-label="Cambiar estado de la sugerencia">
                                        <option value="1" {% if sugerencia.Estado == 1 %}selected{% endif %}>Activo</option>
                                        <option value="2" {% if sugerencia.Estado == 2 %}selected{% endif %}>Inactivo</option>
                                    </select>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <script src="{{ url_for('static', filename='js/notifications.js') }}"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const selects = document.querySelectorAll('.estado-sugerencia');

        selects.forEach(select => {
            select.addEventListener('change', function() {
                const id = this.dataset.id;
                const nuevoEstado = this.value;

                fetch(`/sugerencias/actualizar/${id}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `estado=${nuevoEstado}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Actualizar el badge de estado
                        const badge = this.closest('tr').querySelector('.badge');
                        badge.className = `badge ${nuevoEstado == 1 ? 'badge-success' : 'badge-secondary'}`;
                        badge.textContent = nuevoEstado == 1 ? 'Activo' : 'Inactivo';

                        // Mostrar notificación de éxito con el sistema de notificaciones animadas
                        showSuccess(data.message);
                    } else {
                        showError(data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showError('Error al actualizar el estado');
                });
            });
        });

        // Convertir los mensajes flash a notificaciones animadas
        const flashMessages = document.querySelectorAll('.flash-message');
        flashMessages.forEach(message => {
            const type = message.dataset.type || 'info';
            const messageText = message.textContent.trim();
            if (type === 'success') {
                showSuccess(messageText);
            } else if (type === 'error') {
                showError(messageText);
            } else if (type === 'warning') {
                showWarning(messageText);
            } else {
                showInfo(messageText);
            }
            message.remove();
        });
    });
    </script>
</body>
</html>