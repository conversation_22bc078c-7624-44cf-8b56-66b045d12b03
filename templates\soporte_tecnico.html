<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Soporte Técnico - VerdeQR</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/inicio.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/buttons.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/responsive_large.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/notifications.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/footer_inicio.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .contenido-pagina {
            max-width: 1200px;
            margin: 40px auto;
            padding: 30px;
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .contenido-pagina h1 {
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
            font-size: 2.2rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 15px;
        }

        .contenido-pagina h2 {
            color: #2c3e50;
            margin: 25px 0 15px;
            font-size: 1.6rem;
        }

        .contenido-pagina p {
            margin-bottom: 20px;
            line-height: 1.6;
            color: #333;
        }

        .soporte-info {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            margin-top: 40px;
        }

        .soporte-card {
            flex: 1 1 300px;
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .soporte-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .soporte-card h3 {
            color: #3498db;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .soporte-card i {
            font-size: 1.5rem;
        }

        .soporte-card p {
            margin-bottom: 15px;
        }

        .soporte-card .btn {
            display: inline-block;
            margin-top: 10px;
        }

        .contacto-form {
            margin-top: 40px;
            background-color: #f8f9fa;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .contacto-form h2 {
            margin-top: 0;
            border-bottom: 1px solid #ddd;
            padding-bottom: 15px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            border-color: #3498db;
            outline: none;
        }

        .form-group textarea {
            min-height: 150px;
            resize: vertical;
        }

        .btn-enviar {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.3s ease;
        }

        .btn-enviar:hover {
            background-color: #2980b9;
        }

        .footer-principal {
            margin-top: auto;
        }
    </style>
</head>
<body class="gestion-body">
    {% include 'flash_messages.html' %}
    <!-- Menú superior simple -->
    <header class="menu-simple" style="background-color: #2c3e50; color: white; padding: 8px 20px; display: flex; justify-content: space-between; align-items: center; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); position: fixed; width: 100%; top: 0; z-index: 1000; height: 60px;">
        <div class="logo">
            <img src="{{ url_for('static', filename='css/js/img/logo.png') }}" alt="Logo VerdeQR" style="height: 40px; width: 40px; border-radius: 50%; object-fit: cover;">
        </div>
        <div class="botones" style="display: flex; align-items: center; gap: 15px; margin-right: auto; margin-left: 20px;">
            <a href="{{ url_for('preguntas_frecuentes') }}" style="color: white; text-decoration: none; display: flex; align-items: center; gap: 5px; transition: all 0.2s ease;"><i class="fas fa-question-circle"></i> Preguntas frecuentes</a>
            <a href="{{ url_for('reportar_problema') }}" style="color: white; text-decoration: none; display: flex; align-items: center; gap: 5px; transition: all 0.2s ease;"><i class="fas fa-bug"></i> Reportar problema</a>
            <a href="{{ url_for('inicio') }}" style="color: white; text-decoration: none; display: flex; align-items: center; gap: 5px; transition: all 0.2s ease;"><i class="fas fa-home"></i> Volver a inicio</a>
        </div>
        <div class="usuario-botones" style="display: flex; align-items: center; gap: 10px;">
            {% if 'usuario' in session %}
                <a href="{{ url_for('perfil') }}" class="btn" style="display: flex; align-items: center; gap: 10px;">
                    <img src="{{ url_for('static', filename='css/js/img/perfil.png') }}" alt="Usuario" style="width: 32px; height: 32px; border-radius: 50%;">
                    <div style="text-align: left;">
                        <div style="font-weight: bold;">{{ session['usuario']['Nombres'] }} {{ session['usuario']['Apellidos'] }}</div>
                        <div style="font-size: 0.8em;">{{ session['usuario']['Correo'] }}</div>
                    </div>
                </a>
            {% else %}
                <a href="{{ url_for('iniciar_sesion') }}" class="btn">Iniciar Sesión</a>
                <a href="{{ url_for('registro_usuario') }}" class="btn btn-registro">Registrarse</a>
            {% endif %}
        </div>
    </header>

    <!-- Contenido principal -->
    <div class="contenido-pagina">
        <h1>Soporte Técnico</h1>

        <p>Bienvenido al centro de soporte técnico de VerdeQR. Estamos aquí para ayudarte con cualquier problema técnico que puedas encontrar mientras utilizas nuestra plataforma.</p>

        <div class="soporte-info">
            <div class="soporte-card">
                <h3><i class="fas fa-headset"></i> Asistencia en línea</h3>
                <p>Nuestro equipo de soporte está disponible de lunes a viernes de 8:00 AM a 6:00 PM para ayudarte con cualquier problema técnico.</p>
                <p>Tiempo promedio de respuesta: 24 horas</p>
                <a href="mailto:<EMAIL>" class="btn">Contactar soporte</a>
            </div>

            <div class="soporte-card">
                <h3><i class="fas fa-book"></i> Documentación</h3>
                <p>Consulta nuestra documentación detallada sobre cómo utilizar todas las funciones de VerdeQR, incluyendo guías paso a paso y tutoriales en video.</p>
                <a href="{{ url_for('preguntas_frecuentes') }}" class="btn">Ver documentación</a>
            </div>

            <div class="soporte-card">
                <h3><i class="fas fa-bug"></i> Reportar problemas</h3>
                <p>¿Encontraste un error o problema en la plataforma? Repórtalo para que podamos solucionarlo lo antes posible.</p>
                <a href="{{ url_for('reportar_problema') }}" class="btn">Reportar problema</a>
            </div>
        </div>

        <div class="contacto-form">
            <h2>Formulario de contacto</h2>
            <form action="#" method="POST">
                <div class="form-group">
                    <label for="nombre">Nombre completo</label>
                    <input type="text" id="nombre" name="nombre" required>
                </div>

                <div class="form-group">
                    <label for="email">Correo electrónico</label>
                    <input type="email" id="email" name="email" required>
                </div>

                <div class="form-group">
                    <label for="asunto">Asunto</label>
                    <select id="asunto" name="asunto" required>
                        <option value="">Selecciona un asunto</option>
                        <option value="problema_tecnico">Problema técnico</option>
                        <option value="consulta">Consulta general</option>
                        <option value="sugerencia">Sugerencia</option>
                        <option value="otro">Otro</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="mensaje">Mensaje</label>
                    <textarea id="mensaje" name="mensaje" required></textarea>
                </div>

                <button type="submit" class="btn-enviar">Enviar mensaje</button>
            </form>
        </div>
    </div>

    <!-- Pie de Página -->
    <footer class="footer-principal">
        <div class="contenido-footer">
            <div class="logo-footer">
                <div class="logo-circular">
                    <img src="{{ url_for('static', filename='css/js/img/logo.png') }}" alt="Logo VerdeQR">
                </div>
                <p>Plataforma de conservación y conocimiento de la flora colombiana</p>
            </div>
            <div class="enlaces-footer">
                <div class="columna">
                    <h4>Enlaces rápidos</h4>
                    <ul>
                        <li><a href="{{ url_for('inicio') }}">Inicio</a></li>
                        <li><a href="{{ url_for('principal') }}#arboles">Árboles</a></li>
                        <li><a href="{{ url_for('principal') }}#centros">Centros</a></li>
                        <li><a href="{{ url_for('principal') }}#sugerencias">Sugerencias</a></li>
                    </ul>
                </div>
                <div class="columna">
                    <h4>Contacto</h4>
                    <ul>
                        <li><a href="{{ url_for('soporte_tecnico') }}">Soporte técnico</a></li>
                        <li><a href="{{ url_for('preguntas_frecuentes') }}">Preguntas frecuentes</a></li>
                        <li><a href="{{ url_for('reportar_problema') }}">Reportar problema</a></li>
                        <li><a href="{{ url_for('inicio') }}">Inicio</a></li>
                    </ul>
                </div>
            </div>
            <div class="redes-sociales">
                <h4>Síguenos</h4>
                <div class="iconos-redes">
                    <a href="#" title="Facebook"><i class="fab fa-facebook"></i></a>
                    <a href="#" title="Twitter"><i class="fab fa-twitter"></i></a>
                    <a href="#" title="Instagram"><i class="fab fa-instagram"></i></a>
                    <a href="#" title="YouTube"><i class="fab fa-youtube"></i></a>
                </div>
            </div>
        </div>
        <div class="derechos">
            <p>&copy; 2024 VerdeQR. Todos los derechos reservados.</p>
        </div>
    </footer>

    <script src="{{ url_for('static', filename='js/notifications.js') }}"></script>
</body>
</html>
