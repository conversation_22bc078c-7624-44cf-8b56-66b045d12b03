/* inicio_enhanced.css - Mejoras visuales para la página de inicio */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

/* Variables mejoradas que complementan las existentes */
:root {
  /* Colores mejorados */
  --color-primario-mejorado: #1e5631;
  --color-secundario-mejorado: #3498db;
  --color-acento-mejorado: #27ae60;
  --color-acento-hover: #2ecc71;

  /* Sistema de sombras */
  --sombra-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --sombra-md: 0 4px 8px rgba(0, 0, 0, 0.08);
  --sombra-lg: 0 8px 16px rgba(0, 0, 0, 0.1);

  /* Bordes redondeados */
  --borde-radio-sm: 8px;
  --borde-radio-md: 12px;
  --borde-radio-lg: 16px;
  --borde-radio-completo: 9999px;
}

/* Mejoras generales */
body {
  font-family: 'Montserrat', 'Poppins', sans-serif;
}

/* Mejoras al menú superior */
.menu-superior {
  background-color: var(--color-primario-mejorado);
  box-shadow: var(--sombra-md);
  transition: padding 0.3s ease, background-color 0.3s ease;
}

.menu-superior.scrolled {
  padding: 5px 20px;
  background-color: rgba(30, 86, 49, 0.95);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.menu-superior .logo img {
  transition: transform 0.3s ease, border-color 0.3s ease;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.menu-superior .logo img:hover {
  transform: scale(1.05) rotate(5deg);
  border-color: white;
}

.menu-superior .buscador input {
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: var(--borde-radio-completo);
  transition: all 0.3s ease;
}

.menu-superior .buscador input:focus {
  background-color: rgba(255, 255, 255, 0.25);
  box-shadow: 0 0 0 2px rgba(46, 204, 113, 0.5);
}

.menu-superior .buscador button:hover {
  transform: translateY(-50%) scale(1.1);
  color: var(--color-acento-mejorado);
}

/* Mejoras a los botones */
.btn {
  border-radius: var(--borde-radio-completo);
  box-shadow: var(--sombra-sm);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  background-color: var(--color-acento-mejorado);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255,255,255,0.1), rgba(255,255,255,0));
  transform: translateY(-100%);
  transition: transform 0.3s ease;
}

.btn:hover {
  transform: translateY(-3px);
  box-shadow: var(--sombra-md);
  background-color: var(--color-acento-hover);
}

.btn:hover::before {
  transform: translateY(0);
}

.btn:active {
  transform: translateY(-1px);
  box-shadow: var(--sombra-sm);
}

/* Mejoras al perfil de usuario */
.btn-perfil {
  transition: all 0.3s ease;
}

.btn-perfil:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: var(--sombra-md);
}

.btn-perfil:hover .user-avatar {
  border-color: white;
  transform: scale(1.05);
}

/* Mejoras a la navegación rápida */
.navegacion-rapida {
  background-color: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  box-shadow: var(--sombra-sm);
  position: fixed;
  top: 70px; /* Altura inicial del menú */
  transition: top 0.3s ease;
}

.nav-btn {
  position: relative;
  transition: all 0.3s ease;
}

.nav-btn::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: var(--color-acento-mejorado);
  transition: width 0.3s ease;
  transform: translateX(-50%);
}

.nav-btn:hover::after,
.nav-btn.active::after {
  width: 80%;
}

.nav-btn:hover,
.nav-btn.active {
  background-color: rgba(39, 174, 96, 0.1);
  color: var(--color-acento-mejorado);
}

/* Mejoras al cuadro QR */
.cuadro-qr {
  border-radius: var(--borde-radio-lg);
  box-shadow: var(--sombra-lg);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.cuadro-qr::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--color-acento-mejorado), var(--color-secundario-mejorado));
}

.cuadro-qr:hover {
  transform: translateY(-8px);
  box-shadow: var(--sombra-lg);
}

.cuadro-qr img {
  transition: all 0.3s ease;
}

.cuadro-qr:hover img {
  transform: scale(1.05);
}

.btn-scan {
  background: linear-gradient(to right, var(--color-acento-mejorado), var(--color-acento-hover));
  border-radius: var(--borde-radio-completo);
  box-shadow: var(--sombra-md);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-scan::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255,255,255,0.2), rgba(255,255,255,0));
  transform: translateY(-100%);
  transition: transform 0.3s ease;
}

.btn-scan:hover {
  transform: translateY(-3px);
  box-shadow: var(--sombra-lg);
}

.btn-scan:hover::before {
  transform: translateY(0);
}

/* Mejoras a las tarjetas de árboles */
.grid-item-arbol {
  border-radius: var(--borde-radio-md);
  box-shadow: var(--sombra-md);
  transition: all 0.3s ease;
  overflow: hidden;
}

.grid-item-arbol:hover {
  transform: translateY(-8px);
  box-shadow: var(--sombra-lg);
}

.grid-item-arbol img {
  transition: all 0.5s ease;
}

.grid-item-arbol:hover img {
  transform: scale(1.05);
}

/* Mejoras a las tarjetas de tipos */
.grid-item-tipo {
  border-radius: var(--borde-radio-md);
  box-shadow: var(--sombra-md);
  transition: all 0.3s ease;
}

.grid-item-tipo:hover {
  transform: translateY(-8px);
  box-shadow: var(--sombra-lg);
}

.flecha img {
  transition: all 0.3s ease;
}

.grid-item-tipo:hover .flecha img {
  transform: translateX(5px);
  opacity: 1;
}

/* Mejoras a las tarjetas de centros */
.grid-item-centro {
  border-radius: var(--borde-radio-md);
  box-shadow: var(--sombra-md);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.grid-item-centro:hover {
  transform: translateY(-8px);
  box-shadow: var(--sombra-lg);
}

.grid-item-centro img {
  transition: all 0.5s ease;
}

.grid-item-centro:hover img {
  transform: scale(1.05);
}

.btn-centro {
  display: inline-block;
  margin-top: 10px;
  padding: 8px 16px;
  background-color: var(--color-acento-mejorado);
  color: white;
  border-radius: var(--borde-radio-completo);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: var(--sombra-sm);
}

.btn-centro:hover {
  background-color: var(--color-acento-hover);
  transform: translateY(-3px);
  box-shadow: var(--sombra-md);
}

.overlay-link {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.btn-centro {
  position: relative;
  z-index: 2;
}

.flecha a {
  display: block;
  width: 100%;
  height: 100%;
}

/* Mejoras a las tarjetas de beneficios */
.grid-item-beneficio {
  border-radius: var(--borde-radio-md);
  box-shadow: var(--sombra-md);
  transition: all 0.3s ease;
}

.grid-item-beneficio:hover {
  transform: translateY(-8px);
  box-shadow: var(--sombra-lg);
}

.tooltip-beneficio {
  border-radius: var(--borde-radio-md);
  transition: opacity 0.3s ease;
}

/* Mejoras al footer */
.footer-red {
  transition: all 0.3s ease;
}

.footer-red:hover {
  transform: translateY(-5px) scale(1.1);
  background-color: var(--color-acento-mejorado);
}

/* Mejoras a los botones flotantes */
.btn-flotante {
  border-radius: var(--borde-radio-completo);
  box-shadow: var(--sombra-md);
  transition: all 0.3s ease;
}

.btn-flotante:hover {
  transform: translateY(-5px);
  box-shadow: var(--sombra-lg);
}

/* Animaciones para elementos al hacer scroll */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-on-scroll {
  opacity: 0;
  will-change: opacity, transform;
}

.animate-on-scroll.animated {
  animation: fadeInUp 0.6s ease forwards;
}

/* Mejoras de responsive design */

/* Para pantallas extra grandes (1400px y superior) */
@media (min-width: 1400px) {
  .contenido-principal,
  .segunda-pantalla,
  .tercera-pantalla,
  .cuarta-pantalla,
  .quinta-pantalla,
  .beneficios-pantalla {
    max-width: 1320px;
    margin-left: auto;
    margin-right: auto;
  }

  .contenido-principal h1 {
    font-size: 3.2rem;
  }

  .contenido-principal h3 {
    font-size: 1.5rem;
    max-width: 900px;
  }

  .cuadro-qr {
    max-width: 500px;
  }

  .grid-container-arboles {
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
  }

  .grid-container-tipos {
    grid-template-columns: repeat(3, 1fr);
    gap: 25px;
  }

  .grid-container-beneficios {
    grid-template-columns: repeat(3, 1fr);
    gap: 25px;
  }
}

/* Para pantallas grandes (1200px a 1399px) */
@media (min-width: 1200px) and (max-width: 1399px) {
  .contenido-principal,
  .segunda-pantalla,
  .tercera-pantalla,
  .cuarta-pantalla,
  .quinta-pantalla,
  .beneficios-pantalla {
    max-width: 1140px;
  }

  .grid-container-arboles {
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
  }

  .grid-container-tipos {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-container-beneficios {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Para tablets (768px a 991px) */
@media (max-width: 991px) {
  .menu-superior {
    padding: 10px 15px;
  }

  .menu-superior .buscador {
    max-width: 300px;
    margin: 0 10px;
  }

  .segunda-pantalla {
    flex-direction: column;
    padding: 60px 20px;
  }

  .segunda-pantalla .texto,
  .segunda-pantalla .imagen {
    width: 100%;
    padding: 0;
    margin-bottom: 30px;
  }

  .grid-container-arboles {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-container-tipos {
    grid-template-columns: 1fr;
  }

  .grid-container-beneficios {
    grid-template-columns: repeat(2, 1fr);
  }

  .navegacion-rapida {
    overflow-x: auto;
    justify-content: flex-start;
    padding: 10px;
  }

  .nav-btn {
    white-space: nowrap;
  }
}

/* Para móviles (hasta 767px) */
@media (max-width: 767px) {
  .menu-superior {
    flex-wrap: wrap;
    padding: 10px;
  }

  .menu-superior .logo {
    margin-right: auto;
  }

  .menu-superior .buscador {
    order: 3;
    max-width: 100%;
    width: 100%;
    margin: 10px 0 0;
  }

  .menu-superior .botones {
    gap: 5px;
  }

  .btn {
    padding: 6px 12px;
    font-size: 0.9rem;
  }

  .contenido-principal {
    padding: 100px 15px 40px;
  }

  .contenido-principal h1 {
    font-size: 2rem;
  }

  .contenido-principal h3 {
    font-size: 1.1rem;
  }

  .cuadro-qr {
    padding: 20px;
  }

  .grid-container-arboles {
    grid-template-columns: 1fr;
  }

  .grid-container-beneficios {
    grid-template-columns: 1fr;
  }

  .grid-item-tipo {
    flex-direction: column;
  }

  .imagen-tipo {
    width: 100%;
    height: 150px;
  }

  .navegacion-rapida {
    top: 60px !important;
    padding: 5px;
  }

  .nav-btn {
    padding: 5px 10px;
    font-size: 0.9rem;
  }

  .footer-enlaces {
    flex-direction: column;
    gap: 10px;
  }

  .btn-flotante {
    padding: 8px 15px;
    font-size: 0.9rem;
  }
}
