/* Estilos generales */
body {
    font-family: 'Arial', sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f8f9fa;
}

/* Estilos del header */
.header-principal {
    background-color: #2c3e50;
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.logo-container {
    display: flex;
    align-items: center;
}

.logo {
    height: 40px;
    margin-right: 1rem;
}

/* Estilos del menú */
.menu-principal {
    flex-grow: 1;
    margin: 0 2rem;
}

#menu {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    gap: 1rem;
}

#menu li a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.3s;
}

#menu li a:hover {
    background-color: #34495e;
}

#menu li a i {
    margin-right: 0.5rem;
}

/* Estilos de la sección de usuario */
.user-section {
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 1rem;
}

.user-info {
    display: flex;
    flex-direction: column;
  }

.user-name {
    font-weight: bold;
}

.user-email {
    font-size: 0.8rem;
    opacity: 0.8;
  }

/* Estilos de las notificaciones */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.alert {
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Estilos de las tarjetas */
.card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem;
}

.card-body {
    padding: 1.5rem;
}

/* Estilos de los formularios */
.form-group {
    margin-bottom: 1rem;
}

.form-control {
    border-radius: 4px;
    border: 1px solid #ced4da;
    padding: 0.5rem;
    width: 100%;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* Estilos de los botones */
.btn {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

/* Estilos de las tablas */
.table {
    width: 100%;
    margin-bottom: 1rem;
    background-color: transparent;
}

.table th,
.table td {
    padding: 0.75rem;
    vertical-align: top;
    border-top: 1px solid #dee2e6;
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
    background-color: #f8f9fa;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,0.05);
}

/* Estilos responsivos */
@media (max-width: 768px) {
    .header-principal {
        flex-direction: column;
        padding: 1rem;
    }

    .menu-principal {
        margin: 1rem 0;
    }

    #menu {
        flex-direction: column;
        gap: 0.5rem;
    }

    .user-section {
        margin-top: 1rem;
    }

    .card-body {
        padding: 1rem;
    }
  }

/* Estilos para el código QR */
.qr-container {
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.qr-image {
    border: 1px solid #eee;
    padding: 10px;
    background-color: white;
    border-radius: 4px;
}

