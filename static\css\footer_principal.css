/* Estilos para el footer de las páginas enlazadas desde principal.html */
.footer {
    background-color: var(--color-primario, #2c3e50);
    color: white;
    padding: 40px 20px;
    margin-top: 50px;
    width: 100%;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
}

.footer-logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.footer-logo img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-bottom: 10px;
    border: 2px solid var(--color-acento, #28a745);
}

.footer-logo h3 {
    font-size: 1.5rem;
    margin-bottom: 5px;
    color: white;
}

.footer-logo p {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

.footer-links h4,
.footer-contact h4 {
    color: white;
    margin-bottom: 15px;
    font-size: 1.2rem;
    border-bottom: 2px solid var(--color-acento, #28a745);
    padding-bottom: 8px;
    display: inline-block;
}

.footer-links ul {
    list-style: none;
    padding: 0;
}

.footer-links ul li {
    margin-bottom: 10px;
}

.footer-links ul li a {
    color: rgba(255, 255, 255, 0.7);
    transition: color 0.3s;
    display: flex;
    align-items: center;
}

.footer-links ul li a:hover {
    color: var(--color-acento, #28a745);
}

.footer-contact p {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.7);
}

.footer-contact p i {
    margin-right: 10px;
    color: var(--color-acento, #28a745);
    width: 20px;
    text-align: center;
}

.footer-redes {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
}

.footer-red {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transition: all 0.3s;
}

.footer-red:hover {
    background-color: var(--color-acento, #28a745);
    transform: translateY(-3px);
}

.footer-red i {
    color: white;
    font-size: 1.2rem;
}

.footer-derechos {
    grid-column: 1 / -1;
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.footer-derechos p {
    margin-bottom: 5px;
}

.footer-derechos .fa-heart {
    color: var(--color-acento, #28a745);
}

@media (max-width: 768px) {
    .footer-container {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .footer-contact p {
        justify-content: center;
    }
    
    .footer-links ul li a {
        justify-content: center;
    }
}
