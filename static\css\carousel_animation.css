/* Estilos mejorados para la animación del carrusel */

/* Contenedor del carrusel */
.carrusel-container {
    position: relative;
    overflow: hidden;
    padding: 0 40px;
    min-height: 300px;
    display: flex;
    align-items: center;
    flex-direction: column;
}

/* Carrusel de sugerencias */
.carrusel-sugerencias {
    display: flex;
    gap: 20px;
    transition: all 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    width: 100%;
    flex-wrap: nowrap;
    min-height: 250px;
}

/* Tarjetas de sugerencias */
.sugerencia-card {
    flex: 0 0 calc(20% - 20px);
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    opacity: 1;
    transform: none;
    position: relative;
    min-width: 200px;
    will-change: transform, opacity;
}

/* Efecto hover en las tarjetas */
.sugerencia-card:hover {
    transform: translateY(-8px) scale(1.03);
    box-shadow: 0 15px 30px rgba(0,0,0,0.2);
    z-index: 5;
}

/* Botones de navegación */
.carrusel-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: #2c3e50;
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 10;
    margin: 0;
}

.carrusel-btn:hover {
    background: #3498db;
    transform: translateY(-50%) scale(1.1);
}

.carrusel-btn.prev {
    left: 0;
}

.carrusel-btn.next {
    right: 0;
}

/* Indicadores de página */
.carousel-pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    gap: 8px;
}

.carousel-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(52, 152, 219, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.carousel-indicator.active {
    background-color: #3498db;
    transform: scale(1.2);
}

.carousel-indicator:hover {
    background-color: rgba(52, 152, 219, 0.7);
}

/* Animaciones */
@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(50px);
    }
}

@keyframes fadeOutLeft {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(-50px);
    }
}

/* Estilos responsivos */
@media (max-width: 1399px) {
    .sugerencia-card {
        flex: 0 0 calc(25% - 20px);
    }
}

@media (max-width: 1199px) {
    .sugerencia-card {
        flex: 0 0 calc(33.333% - 20px);
    }
}

@media (max-width: 991px) {
    .sugerencia-card {
        flex: 0 0 calc(50% - 20px);
    }
}

@media (max-width: 767px) {
    .sugerencia-card {
        flex: 0 0 calc(100% - 20px);
    }
}
