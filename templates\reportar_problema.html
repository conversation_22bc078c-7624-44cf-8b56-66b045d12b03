<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reportar Problema - VerdeQR</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/inicio.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/buttons.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/responsive_large.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/notifications.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/footer_inicio.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .contenido-pagina {
            max-width: 1200px;
            margin: 40px auto;
            padding: 30px;
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .contenido-pagina h1 {
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
            font-size: 2.2rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 15px;
        }

        .contenido-pagina p {
            margin-bottom: 20px;
            line-height: 1.6;
            color: #333;
        }

        .reporte-container {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            margin-top: 30px;
        }

        .reporte-info {
            flex: 1;
            min-width: 300px;
        }

        .reporte-form {
            flex: 2;
            min-width: 500px;
            background-color: #f8f9fa;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .info-card {
            background-color: #e9f7fe;
            border-left: 4px solid #3498db;
            padding: 15px 20px;
            margin-bottom: 20px;
            border-radius: 0 4px 4px 0;
        }

        .info-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .info-card p {
            margin-bottom: 10px;
            font-size: 0.95rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            border-color: #3498db;
            outline: none;
        }

        .form-group textarea {
            min-height: 150px;
            resize: vertical;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        .btn-enviar {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.3s ease;
        }

        .btn-enviar:hover {
            background-color: #2980b9;
        }

        .file-upload {
            position: relative;
            overflow: hidden;
            margin-top: 10px;
            display: inline-block;
        }

        .file-upload input[type="file"] {
            position: absolute;
            top: 0;
            right: 0;
            min-width: 100%;
            min-height: 100%;
            font-size: 100px;
            text-align: right;
            filter: alpha(opacity=0);
            opacity: 0;
            outline: none;
            background: white;
            cursor: pointer;
            display: block;
        }

        .file-upload-btn {
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            color: #333;
            padding: 8px 15px;
            border-radius: 4px;
            display: inline-block;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .file-upload-btn:hover {
            background-color: #e0e0e0;
        }

        .file-name {
            margin-left: 10px;
            font-size: 0.9rem;
            color: #666;
        }

        .footer-principal {
            margin-top: auto;
        }

        @media (max-width: 768px) {
            .reporte-container {
                flex-direction: column;
            }

            .reporte-form {
                min-width: auto;
            }

            .form-row {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body class="gestion-body">
    {% include 'flash_messages.html' %}
    <!-- Menú superior simple -->
    <header class="menu-simple" style="background-color: #2c3e50; color: white; padding: 8px 20px; display: flex; justify-content: space-between; align-items: center; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); position: fixed; width: 100%; top: 0; z-index: 1000; height: 60px;">
        <div class="logo">
            <img src="{{ url_for('static', filename='css/js/img/logo.png') }}" alt="Logo VerdeQR" style="height: 40px; width: 40px; border-radius: 50%; object-fit: cover;">
        </div>
        <div class="botones" style="display: flex; align-items: center; gap: 15px; margin-right: auto; margin-left: 20px;">
            <a href="{{ url_for('soporte_tecnico') }}" style="color: white; text-decoration: none; display: flex; align-items: center; gap: 5px; transition: all 0.2s ease;"><i class="fas fa-headset"></i> Soporte técnico</a>
            <a href="{{ url_for('preguntas_frecuentes') }}" style="color: white; text-decoration: none; display: flex; align-items: center; gap: 5px; transition: all 0.2s ease;"><i class="fas fa-question-circle"></i> Preguntas frecuentes</a>
            <a href="{{ url_for('inicio') }}" style="color: white; text-decoration: none; display: flex; align-items: center; gap: 5px; transition: all 0.2s ease;"><i class="fas fa-home"></i> Volver a inicio</a>
        </div>
        <div class="usuario-botones" style="display: flex; align-items: center; gap: 10px;">
            {% if 'usuario' in session %}
                <a href="{{ url_for('perfil') }}" class="btn" style="display: flex; align-items: center; gap: 10px;">
                    <img src="{{ url_for('static', filename='css/js/img/perfil.png') }}" alt="Usuario" style="width: 32px; height: 32px; border-radius: 50%;">
                    <div style="text-align: left;">
                        <div style="font-weight: bold;">{{ session['usuario']['Nombres'] }} {{ session['usuario']['Apellidos'] }}</div>
                        <div style="font-size: 0.8em;">{{ session['usuario']['Correo'] }}</div>
                    </div>
                </a>
            {% else %}
                <a href="{{ url_for('iniciar_sesion') }}" class="btn">Iniciar Sesión</a>
                <a href="{{ url_for('registro_usuario') }}" class="btn btn-registro">Registrarse</a>
            {% endif %}
        </div>
    </header>

    <!-- Contenido principal -->
    <div class="contenido-pagina">
        <h1>Reportar Problema</h1>

        <p>¿Has encontrado un error o problema en nuestra plataforma? Ayúdanos a mejorar VerdeQR reportando cualquier inconveniente que hayas experimentado. Nuestro equipo técnico revisará tu reporte y trabajará para solucionarlo lo antes posible.</p>

        <div class="reporte-container">
            <div class="reporte-info">
                <div class="info-card">
                    <h3>¿Qué tipo de problemas puedo reportar?</h3>
                    <p>Puedes reportar cualquier tipo de problema técnico, como errores en la aplicación, problemas de visualización, dificultades al escanear códigos QR, o cualquier otra funcionalidad que no esté trabajando correctamente.</p>
                </div>

                <div class="info-card">
                    <h3>¿Qué información debo incluir?</h3>
                    <p>Para ayudarnos a resolver el problema más rápidamente, por favor incluye:</p>
                    <ul>
                        <li>Una descripción detallada del problema</li>
                        <li>Los pasos para reproducir el error</li>
                        <li>El dispositivo y navegador que estabas usando</li>
                        <li>Capturas de pantalla si es posible</li>
                    </ul>
                </div>

                <div class="info-card">
                    <h3>Tiempo de respuesta</h3>
                    <p>Nuestro equipo revisa todos los reportes de problemas y trabaja para solucionarlos lo antes posible. El tiempo de respuesta puede variar dependiendo de la complejidad del problema, pero generalmente respondemos dentro de las 48 horas.</p>
                </div>
            </div>

            <div class="reporte-form">
                <h2>Formulario de reporte</h2>
                <form action="#" method="POST" enctype="multipart/form-data">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="nombre">Nombre completo</label>
                            <input type="text" id="nombre" name="nombre" required value="{{ session['usuario']['Nombres'] + ' ' + session['usuario']['Apellidos'] if 'usuario' in session else '' }}">
                        </div>

                        <div class="form-group">
                            <label for="email">Correo electrónico</label>
                            <input type="email" id="email" name="email" required value="{{ session['usuario']['Correo'] if 'usuario' in session else '' }}">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="tipo_problema">Tipo de problema</label>
                        <select id="tipo_problema" name="tipo_problema" required>
                            <option value="">Selecciona un tipo de problema</option>
                            <option value="error_aplicacion">Error en la aplicación</option>
                            <option value="problema_visualizacion">Problema de visualización</option>
                            <option value="problema_qr">Problema con códigos QR</option>
                            <option value="problema_cuenta">Problema con mi cuenta</option>
                            <option value="otro">Otro</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="titulo">Título del problema</label>
                        <input type="text" id="titulo" name="titulo" required placeholder="Ej: Error al escanear código QR">
                    </div>

                    <div class="form-group">
                        <label for="descripcion">Descripción detallada</label>
                        <textarea id="descripcion" name="descripcion" required placeholder="Describe el problema con el mayor detalle posible. Incluye los pasos para reproducirlo."></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="dispositivo">Dispositivo</label>
                            <input type="text" id="dispositivo" name="dispositivo" placeholder="Ej: iPhone 13, Samsung Galaxy S21, PC">
                        </div>

                        <div class="form-group">
                            <label for="navegador">Navegador</label>
                            <input type="text" id="navegador" name="navegador" placeholder="Ej: Chrome, Firefox, Safari">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="capturas">Capturas de pantalla (opcional)</label>
                        <div class="file-upload">
                            <label for="capturas" class="file-upload-btn">Seleccionar archivos</label>
                            <input type="file" id="capturas" name="capturas" multiple accept="image/*">
                            <span class="file-name" id="file-name-display">Ningún archivo seleccionado</span>
                        </div>
                    </div>

                    <button type="submit" class="btn-enviar">Enviar reporte</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Pie de Página -->
    <footer class="footer-principal">
        <div class="contenido-footer">
            <div class="logo-footer">
                <div class="logo-circular">
                    <img src="{{ url_for('static', filename='css/js/img/logo.png') }}" alt="Logo VerdeQR">
                </div>
                <p>Plataforma de conservación y conocimiento de la flora colombiana</p>
            </div>
            <div class="enlaces-footer">
                <div class="columna">
                    <h4>Enlaces rápidos</h4>
                    <ul>
                        <li><a href="{{ url_for('inicio') }}">Inicio</a></li>
                        <li><a href="{{ url_for('principal') }}#arboles">Árboles</a></li>
                        <li><a href="{{ url_for('principal') }}#centros">Centros</a></li>
                        <li><a href="{{ url_for('principal') }}#sugerencias">Sugerencias</a></li>
                    </ul>
                </div>
                <div class="columna">
                    <h4>Contacto</h4>
                    <ul>
                        <li><a href="{{ url_for('soporte_tecnico') }}">Soporte técnico</a></li>
                        <li><a href="{{ url_for('preguntas_frecuentes') }}">Preguntas frecuentes</a></li>
                        <li><a href="{{ url_for('reportar_problema') }}">Reportar problema</a></li>
                        <li><a href="{{ url_for('inicio') }}">Inicio</a></li>
                    </ul>
                </div>
            </div>
            <div class="redes-sociales">
                <h4>Síguenos</h4>
                <div class="iconos-redes">
                    <a href="#" title="Facebook"><i class="fab fa-facebook"></i></a>
                    <a href="#" title="Twitter"><i class="fab fa-twitter"></i></a>
                    <a href="#" title="Instagram"><i class="fab fa-instagram"></i></a>
                    <a href="#" title="YouTube"><i class="fab fa-youtube"></i></a>
                </div>
            </div>
        </div>
        <div class="derechos">
            <p>&copy; 2024 VerdeQR. Todos los derechos reservados.</p>
        </div>
    </footer>

    <script src="{{ url_for('static', filename='js/notifications.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mostrar nombre de archivos seleccionados
            const fileInput = document.getElementById('capturas');
            const fileNameDisplay = document.getElementById('file-name-display');

            fileInput.addEventListener('change', function() {
                if (fileInput.files.length > 0) {
                    if (fileInput.files.length === 1) {
                        fileNameDisplay.textContent = fileInput.files[0].name;
                    } else {
                        fileNameDisplay.textContent = `${fileInput.files.length} archivos seleccionados`;
                    }
                } else {
                    fileNameDisplay.textContent = 'Ningún archivo seleccionado';
                }
            });

            // Validación del formulario
            const form = document.querySelector('form');

            form.addEventListener('submit', function(event) {
                event.preventDefault();

                // Simulación de envío exitoso
                showSuccess('Tu reporte ha sido enviado correctamente. Nuestro equipo lo revisará pronto.');

                // Resetear el formulario
                form.reset();
                fileNameDisplay.textContent = 'Ningún archivo seleccionado';
            });
        });
    </script>
</body>
</html>
