<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}VerdeQR{% endblock %}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/estilos.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body class="fondo-gestion">
    {% include 'flash_messages.html' %}

    <header class="header-principal header-custom">
        <div class="logo-container">
            <a href="{{ url_for('principal') }}">
                <img src="{{ url_for('static', filename='css/js/img/logo.png') }}" alt="Logo VerdeQR" class="logo logo-circular">
            </a>
        </div>
        <div class="user-section">
            {% if session.get('usuario') %}
            <a href="{{ url_for('perfil') }}" class="user-link">
                {% if session['usuario'].get('Imagen') %}
                    <img src="{{ url_for('static', filename=session['usuario']['Imagen']) }}" alt="Usuario" class="user-avatar">
                {% else %}
                    {% if determinar_genero(session['usuario']['Nombres']) == 'femenino' %}
                        <img src="{{ url_for('static', filename='css/js/img/avatarf.jpg') }}" alt="Usuario" class="user-avatar">
                    {% else %}
                        <img src="{{ url_for('static', filename='css/js/img/avatarm.jpg') }}" alt="Usuario" class="user-avatar">
                    {% endif %}
                {% endif %}
                <div class="user-info">
                    <span class="user-name">{{ session['usuario']['Nombres'] }} {{ session['usuario']['Apellidos'] }}</span>
                    <span class="user-email">{{ session['usuario']['Correo'] }}</span>
                </div>
            </a>
            {% endif %}
        </div>
    </header>

    <main class="w-100 p-0">
    <style>
        .fondo-gestion {
            background-image: url("{{ url_for('static', filename='css/js/img/fgestion.jpg') }}");
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            background-repeat: no-repeat;
            position: relative;
        }

        .fondo-gestion::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: -1;
        }

        .header-custom {
            justify-content: space-between;
            padding: 10px 20px;
            background-color: #2c3e50;
        }

        .logo-circular {
            border-radius: 50%;
            width: 60px;
            height: 60px;
            object-fit: cover;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .user-link {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: white;
        }
    </style>
        {% block content %}{% endblock %}
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/notifications.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
