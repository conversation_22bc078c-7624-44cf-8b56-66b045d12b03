/* Estilos para el footer de las páginas enlazadas desde inicio.html */
.footer-principal {
    background-color: var(--color-primario, #2c3e50);
    color: white;
    padding: 40px 20px;
    margin-top: 50px;
    width: 100%;
}

.contenido-footer {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
}

.logo-footer {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.logo-circular {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    background-color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.logo-circular img {
    width: 90%;
    height: 90%;
    object-fit: contain;
}

.logo-footer p {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 10px;
}

.enlaces-footer {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.columna h4 {
    color: white;
    margin-bottom: 15px;
    font-size: 1.2rem;
    border-bottom: 2px solid var(--color-acento, #28a745);
    padding-bottom: 8px;
    display: inline-block;
}

.columna ul {
    list-style: none;
    padding: 0;
}

.columna ul li {
    margin-bottom: 10px;
}

.columna ul li a {
    color: rgba(255, 255, 255, 0.7);
    transition: color 0.3s;
    display: flex;
    align-items: center;
}

.columna ul li a:hover {
    color: var(--color-acento, #28a745);
}

.redes-sociales h4 {
    color: white;
    margin-bottom: 15px;
    font-size: 1.2rem;
    border-bottom: 2px solid var(--color-acento, #28a745);
    padding-bottom: 8px;
    display: inline-block;
}

.iconos-redes {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.iconos-redes a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transition: all 0.3s;
}

.iconos-redes a:hover {
    background-color: var(--color-acento, #28a745);
    transform: translateY(-3px);
}

.iconos-redes i {
    color: white;
    font-size: 1.2rem;
}

.derechos {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .contenido-footer {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .columna ul li a {
        justify-content: center;
    }
    
    .iconos-redes {
        justify-content: center;
    }
}
