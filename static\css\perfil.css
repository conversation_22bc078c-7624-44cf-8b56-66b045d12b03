/* perfil.css - Estilo moderno e interactivo para la página de perfil */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

:root {
  --color-primario: #28a745;
  --color-secundario: #218838;
  --color-acento: #2c3e50;
  --color-fondo: #f8f9fa;
  --color-fondo-oscuro: #f0f2f5;
  --color-tarjeta: #ffffff;
  --color-texto: #333333;
  --color-texto-claro: #666666;
  --color-borde: #e1e4e8;
  --sombra-suave: 0 4px 6px rgba(0, 0, 0, 0.05);
  --sombra-media: 0 6px 12px rgba(0, 0, 0, 0.1);
  --sombra-fuerte: 0 10px 25px rgba(0, 0, 0, 0.15);
  --transicion: all 0.3s ease;
  --radio-borde: 12px;
}

/* Estilos generales */
body {
  margin: 0;
  padding: 0;
  font-family: 'Poppins', sans-serif;
  background-color: var(--color-fondo);
  color: var(--color-texto);
  line-height: 1.6;
}

a {
  text-decoration: none;
  color: var(--color-primario);
  transition: var(--transicion);
}

a:hover {
  color: var(--color-secundario);
}

button {
  cursor: pointer;
  font-family: 'Poppins', sans-serif;
}

h1, h2, h3, h4, h5, h6 {
  margin: 0;
  line-height: 1.3;
  color: var(--color-acento);
}

/* Contenedor principal */
.perfil-container {
  max-width: 1200px;
  margin: 0 auto;
  background-color: var(--color-tarjeta);
  border-radius: var(--radio-borde);
  overflow: hidden;
  box-shadow: var(--sombra-media);
  margin-bottom: 30px;
}

/* Header y portada */
.perfil-header {
  position: relative;
  height: 300px;
}

.portada-full {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.portada-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.portada-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.6));
}

.perfil-header:hover .portada-img {
  transform: scale(1.05);
}

/* Controles de navegación */
.perfil-controls {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
  display: flex;
  gap: 10px;
}

.btn-control {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  color: var(--color-acento);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--sombra-media);
  transition: var(--transicion);
}

.btn-control:hover {
  background-color: var(--color-primario);
  color: white;
  transform: translateY(-3px);
}

.admin-only {
  background-color: rgba(44, 62, 80, 0.9);
  color: white;
}

.admin-only:hover {
  background-color: #34495e;
}

/* Avatar centrado */
.avatar-centrado {
  position: absolute;
  bottom: -70px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 20;
}

.avatar-wrapper {
  position: relative;
  width: 140px;
  height: 140px;
  border-radius: 50%;
  overflow: hidden;
  border: 5px solid white;
  box-shadow: var(--sombra-media);
  transition: transform 0.3s ease;
}

.avatar-wrapper:hover {
  transform: scale(1.05);
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-edit, .avatar-delete {
  position: absolute;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  box-shadow: var(--sombra-suave);
  transition: var(--transicion);
  opacity: 0;
}

.avatar-edit {
  bottom: 5px;
  right: 5px;
  background-color: var(--color-primario);
}

.avatar-delete {
  bottom: 5px;
  left: 5px;
  background-color: #dc3545; /* Color rojo para eliminar */
}

.avatar-wrapper:hover .avatar-edit,
.avatar-wrapper:hover .avatar-delete {
  opacity: 1;
}

.avatar-edit:hover {
  background-color: var(--color-secundario);
  transform: scale(1.1);
}

.avatar-delete:hover {
  background-color: #c82333; /* Rojo más oscuro al hacer hover */
  transform: scale(1.1);
}

/* Contenido principal */
.perfil-content {
  padding: 80px 30px 30px;
}

.perfil-info {
  text-align: center;
  margin-bottom: 30px;
}

.perfil-info h1 {
  font-size: 2rem;
  margin-bottom: 15px;
  font-weight: 600;
  color: var(--color-acento);
}

.user-meta {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
  margin-top: 10px;
}

.user-meta span {
  display: flex;
  align-items: center;
  gap: 5px;
  color: var(--color-texto-claro);
  font-size: 0.9rem;
}

.user-meta i {
  color: var(--color-primario);
}

.user-role {
  background-color: var(--color-fondo-oscuro);
  padding: 5px 10px;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

/* Estadísticas de admin */
.admin-stats {
  margin: 40px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.stat-card {
  background: linear-gradient(135deg, var(--color-fondo-oscuro), white);
  border-radius: var(--radio-borde);
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: var(--sombra-suave);
  transition: var(--transicion);
  border: 1px solid var(--color-borde);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--sombra-media);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background-color: var(--color-primario);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.stat-info h3 {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--color-acento);
}

.stat-info p {
  margin: 0;
  color: var(--color-texto-claro);
  font-size: 0.9rem;
}

/* Pestañas de navegación */
.profile-tabs {
  display: flex;
  margin: 30px 0;
  border-bottom: 1px solid var(--color-borde);
  gap: 10px;
}

.tab-btn {
  background: none;
  border: none;
  padding: 12px 20px;
  font-size: 1rem;
  font-weight: 500;
  color: var(--color-texto-claro);
  position: relative;
  transition: var(--transicion);
}

.tab-btn::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--color-primario);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.tab-btn:hover {
  color: var(--color-acento);
}

.tab-btn.active {
  color: var(--color-primario);
  font-weight: 600;
}

.tab-btn.active::after {
  transform: scaleX(1);
}

.tab-btn i {
  margin-right: 8px;
}

/* Contenido de las pestañas */
.tab-content {
  display: none;
  animation: fadeIn 0.5s ease;
}

.tab-content.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Sección de información */
.info-section {
  background-color: var(--color-fondo-oscuro);
  border-radius: var(--radio-borde);
  padding: 25px;
  margin-bottom: 30px;
}

.info-section h2 {
  font-size: 1.3rem;
  margin-bottom: 20px;
  color: var(--color-acento);
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-section h2 i {
  color: var(--color-primario);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.info-item {
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: var(--sombra-suave);
  transition: var(--transicion);
}

.info-item:hover {
  transform: translateY(-3px);
  box-shadow: var(--sombra-media);
}

.info-item label {
  display: block;
  font-size: 0.85rem;
  font-weight: 500;
  color: var(--color-texto-claro);
  margin-bottom: 5px;
}

.info-item p {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  color: var(--color-texto);
}

.info-item label i {
  margin-right: 5px;
  color: var(--color-primario);
}

/* Sección de configuración */
.settings-section {
  background-color: var(--color-fondo-oscuro);
  border-radius: var(--radio-borde);
  padding: 25px;
  margin-bottom: 30px;
}

.settings-section h2 {
  font-size: 1.3rem;
  margin-bottom: 20px;
  color: var(--color-acento);
  display: flex;
  align-items: center;
  gap: 10px;
}

.settings-section h2 i {
  color: var(--color-primario);
}

.profile-form {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: var(--sombra-suave);
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--color-texto);
  margin-bottom: 8px;
}

.form-group input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid var(--color-borde);
  border-radius: 8px;
  font-size: 1rem;
  transition: var(--transicion);
}

.form-group input:focus {
  border-color: var(--color-primario);
  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.2);
  outline: none;
}

.form-group small {
  display: block;
  margin-top: 5px;
  color: var(--color-texto-claro);
  font-size: 0.8rem;
}

.btn-save {
  background-color: var(--color-primario);
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: var(--transicion);
  box-shadow: var(--sombra-suave);
}

.btn-save:hover {
  background-color: var(--color-secundario);
  transform: translateY(-3px);
  box-shadow: var(--sombra-media);
}

.security-section {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: var(--sombra-suave);
}

.btn-change-password {
  width: 100%;
  background-color: #f8f9fa;
  color: var(--color-acento);
  border: 1px solid var(--color-borde);
  padding: 12px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: var(--transicion);
  margin-top: 15px;
}

.btn-change-password:hover {
  background-color: #e9ecef;
  color: #212529;
  transform: translateY(-2px);
}

.btn-change-password i {
  color: var(--color-primario);
}

/* Botón de cerrar sesión */
.logout-container {
  text-align: center;
  margin: 30px 0;
  padding: 20px;
}

.btn-logout {
  background-color: white;
  color: #dc3545;
  border: 1px solid #dc3545;
  padding: 12px 30px;
  border-radius: 30px;
  font-size: 1rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: var(--transicion);
}

.btn-logout:hover {
  background-color: #dc3545;
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  overflow: auto;
  padding: 50px 0;
  animation: fadeIn 0.3s ease;
}

/* Avatar expandido */
.avatar-wrapper {
  cursor: pointer;
  transition: transform 0.3s ease;
}

.avatar-wrapper:hover {
  transform: scale(1.03);
}

#avatar-modal .modal-content {
  max-width: 500px;
  padding: 20px;
  text-align: center;
}

#avatar-expandido {
  width: 100%;
  max-width: 300px;
  height: auto;
  border-radius: 10px;
  margin-bottom: 20px;
  box-shadow: var(--sombra);
}

.avatar-modal-controls {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

.btn-avatar {
  padding: 10px 15px;
  border-radius: 5px;
  background-color: var(--color-primario);
  color: white;
  border: none;
  cursor: pointer;
  transition: var(--transicion);
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-avatar:hover {
  background-color: var(--color-secundario);
  transform: scale(1.05);
}

.btn-avatar.btn-danger {
  background-color: #dc3545;
}

.btn-avatar.btn-danger:hover {
  background-color: #c82333;
}

.modal-content {
  background-color: white;
  max-width: 500px;
  margin: 0 auto;
  border-radius: 12px;
  padding: 30px;
  position: relative;
  box-shadow: var(--sombra-fuerte);
  animation: slideIn 0.4s ease;
}

.close-modal {
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 1.8rem;
  color: var(--color-texto-claro);
  cursor: pointer;
  transition: var(--transicion);
}

.close-modal:hover {
  color: #dc3545;
}

.modal h2 {
  margin-bottom: 25px;
  text-align: center;
  font-size: 1.5rem;
  color: var(--color-acento);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.modal h2 i {
  color: var(--color-primario);
}

.btn-submit {
  width: 100%;
  background-color: var(--color-primario);
  color: white;
  border: none;
  padding: 12px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: var(--transicion);
  margin-top: 20px;
}

.btn-submit:hover {
  background-color: var(--color-secundario);
  transform: translateY(-3px);
  box-shadow: var(--sombra-media);
}

@keyframes slideIn {
  from { transform: translateY(-50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Media queries para responsividad */
/* Para pantallas grandes */
@media (min-width: 1400px) {
  .perfil-container {
    max-width: 1400px;
  }

  .perfil-header {
    height: 350px;
  }

  .avatar-wrapper {
    width: 160px;
    height: 160px;
  }

  .avatar-centrado {
    bottom: -80px;
  }

  .perfil-content {
    padding: 100px 40px 40px;
  }

  .perfil-info h1 {
    font-size: 2.2rem;
  }

  .perfil-info p {
    font-size: 1.1rem;
  }

  .info-grid,
  .stats-grid {
    gap: 25px;
  }

  .info-item h3,
  .stats-item h3 {
    font-size: 1.3rem;
  }

  .info-item p,
  .stats-item p {
    font-size: 1.1rem;
  }
}

/* Para tablets */
@media (max-width: 992px) {
  .perfil-header {
    height: 280px;
  }

  .avatar-centrado {
    bottom: -65px;
  }

  .avatar-wrapper {
    width: 130px;
    height: 130px;
  }

  .perfil-content {
    padding: 80px 25px 25px;
  }

  .perfil-info h1 {
    font-size: 1.8rem;
  }

  .info-grid,
  .stats-grid {
    gap: 15px;
  }

  .tab-btn {
    padding: 10px 20px;
  }
}

/* Para móviles grandes */
@media (max-width: 768px) {
  .perfil-header {
    height: 250px;
  }

  .avatar-centrado {
    bottom: -60px;
  }

  .avatar-wrapper {
    width: 120px;
    height: 120px;
  }

  .perfil-content {
    padding: 70px 20px 20px;
  }

  .info-grid,
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .user-meta {
    flex-direction: column;
    gap: 10px;
    align-items: center;
  }

  .modal-content {
    width: 90%;
  }

  .perfil-tabs {
    flex-wrap: wrap;
    justify-content: center;
  }

  .tab-btn {
    margin: 5px;
    flex-grow: 1;
    text-align: center;
  }

  .perfil-controls {
    top: 10px;
    left: 10px;
  }
}

/* Para móviles pequeños */
@media (max-width: 480px) {
  .perfil-header {
    height: 200px;
  }

  .avatar-wrapper {
    width: 100px;
    height: 100px;
  }

  .avatar-centrado {
    bottom: -50px;
  }

  .perfil-content {
    padding: 60px 15px 15px;
  }

  .perfil-info h1 {
    font-size: 1.5rem;
  }

  .btn-control {
    width: 36px;
    height: 36px;
  }

  .tab-btn {
    padding: 8px 12px;
    font-size: 0.85rem;
  }

  .info-item h3,
  .stats-item h3 {
    font-size: 1rem;
  }

  .info-item p,
  .stats-item p {
    font-size: 0.9rem;
  }

  .avatar-edit {
    width: 30px;
    height: 30px;
    font-size: 0.9rem;
  }
}

/* Animaciones adicionales */
.pulse {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Efectos hover adicionales */
.hover-float {
  transition: transform 0.3s ease;
}

.hover-float:hover {
  transform: translateY(-5px);
}

/* Efectos de carga */
.loading::after {
  content: '';
  display: inline-block;
  width: 1em;
  height: 1em;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-left: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}