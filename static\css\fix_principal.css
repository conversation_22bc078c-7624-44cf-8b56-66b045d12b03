/* Solución para problemas de responsive design en principal.html */

/* Asegurar que el body ocupe todo el ancho disponible */
html, body {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* <PERSON>eg<PERSON>r que el header ocupe todo el ancho */
.header-principal {
    width: 100% !important;
    max-width: 100% !important;
    left: 0 !important;
    right: 0 !important;
    box-sizing: border-box !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 1000 !important;
}

/* Asegurar que todas las secciones ocupen todo el ancho */
.bienvenida,
.estadisticas,
.arboles-destacados,
.centros,
.sugerencias,
.footer-principal {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
}

/* Contenedores internos con ancho máximo */
.contenido-bienvenida,
.grid-arboles,
.info-centros,
.contenedor-sugerencias,
.contenido-footer {
    max-width: 1400px !important;
    width: 100% !important;
    margin-left: auto !important;
    margin-right: auto !important;
    padding-left: 20px !important;
    padding-right: 20px !important;
    box-sizing: border-box !important;
}

/* Estilos para el carrusel de sugerencias */
.carrusel-container {
    position: relative !important;
    overflow: hidden !important;
    padding: 0 40px !important;
    min-height: 300px !important;
    width: 100% !important;
    max-width: 1400px !important;
    margin: 0 auto !important;
    box-sizing: border-box !important;
}

.carrusel-sugerencias {
    display: flex !important;
    gap: 20px !important;
    width: 100% !important;
    flex-wrap: nowrap !important;
    transition: transform 0.5s ease-in-out !important;
    will-change: transform !important;
}

.sugerencia-card {
    flex: 0 0 calc(20% - 20px) !important;
    min-width: 200px !important;
    max-width: 300px !important;
    background: white !important;
    border-radius: 10px !important;
    padding: 1.5rem !important;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1) !important;
    transition: all 0.5s ease !important;
    animation: fadeIn 0.5s ease forwards !important;
}

/* Animaciones para el carrusel */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Media queries para pantallas grandes */
@media (min-width: 1400px) {
    .header-principal,
    .bienvenida,
    .estadisticas,
    .arboles-destacados,
    .centros,
    .sugerencias,
    .footer-principal {
        width: 100vw !important;
        max-width: 100vw !important;
        left: 0 !important;
        right: 0 !important;
    }
    
    .contenido-bienvenida,
    .grid-arboles,
    .info-centros,
    .contenedor-sugerencias,
    .contenido-footer {
        max-width: 1400px !important;
        padding-left: 40px !important;
        padding-right: 40px !important;
    }
    
    .estadisticas {
        grid-template-columns: repeat(4, 1fr) !important;
    }
    
    .grid-arboles {
        grid-template-columns: repeat(3, 1fr) !important;
    }
    
    .info-centros {
        flex-direction: row !important;
        align-items: center !important;
    }
    
    .contenedor-sugerencias {
        flex-direction: row !important;
    }
}
