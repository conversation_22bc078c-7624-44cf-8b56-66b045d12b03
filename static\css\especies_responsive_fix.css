/* Corrección responsiva específica para la sección "Explora árboles por especie" */

/* Regla general para evitar sobreposición de la flecha con el texto */
.texto-tipo p:last-of-type {
  margin-bottom: 30px; /* Espacio adicional después del último párrafo para la flecha */
}

/* Regla específica para el párrafo de usos */
.texto-tipo p strong {
  display: inline-block;
  margin-bottom: 5px;
}

/* Estilos base para la sección */
.cuarta-pantalla {
  padding: 80px 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.grid-container-tipos {
  display: grid;
  gap: 30px;
}

.grid-item-tipo {
  display: flex;
  background-color: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.grid-item-tipo:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.imagen-tipo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.texto-tipo {
  position: relative;
  padding: 15px;
  padding-bottom: 40px; /* Espacio para la flecha */
}

.texto-tipo h2 {
  font-size: 1rem;
  color: #2c3e50;
  margin-bottom: 5px;
}

.texto-tipo p {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 5px;
  padding-right: 5px; /* Evitar que el texto llegue hasta el borde */
}

.flecha {
  position: absolute;
  bottom: 15px;
  right: 15px;
  z-index: 2;
}

.flecha img {
  width: 20px;
  height: 20px;
  opacity: 0.5;
  transition: all 0.3s ease;
  display: block; /* Asegurar que la imagen se muestre correctamente */
}

.grid-item-tipo:hover .flecha img {
  opacity: 1;
  transform: translateX(3px);
}

/* Móviles pequeños (hasta 480px) */
@media (max-width: 480px) {
  .cuarta-pantalla {
    padding: 40px 15px;
  }

  .cuarta-pantalla h1 {
    font-size: 1.8rem;
    text-align: center;
  }

  .cuarta-pantalla h3 {
    font-size: 1rem;
    text-align: center;
  }

  .grid-container-tipos {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .grid-item-tipo {
    flex-direction: column;
    height: auto;
  }

  .imagen-tipo {
    width: 100%;
    height: 180px;
  }

  .texto-tipo {
    width: 100%;
    padding: 15px;
    padding-bottom: 45px; /* Más espacio para la flecha en móviles */
    min-height: 180px; /* Altura mínima aumentada */
  }

  .flecha {
    bottom: 10px;
    right: 10px;
  }

  .texto-tipo p:last-of-type {
    margin-bottom: 25px; /* Espacio adicional después del último párrafo */
  }
}

/* Móviles (481px - 767px) */
@media (min-width: 481px) and (max-width: 767px) {
  .cuarta-pantalla {
    padding: 50px 20px;
  }

  .grid-container-tipos {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .grid-item-tipo {
    flex-direction: row;
    height: auto;
    min-height: 180px;
  }

  .imagen-tipo {
    width: 150px;
    min-width: 150px;
    height: 100%;
  }

  .texto-tipo {
    width: calc(100% - 150px);
    padding: 15px;
    padding-bottom: 40px; /* Espacio para la flecha */
  }

  .texto-tipo p:last-of-type {
    margin-bottom: 20px; /* Espacio adicional después del último párrafo */
  }
}

/* Tablets (768px - 991px) */
@media (min-width: 768px) and (max-width: 991px) {
  .grid-container-tipos {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .grid-item-tipo {
    flex-direction: column;
    height: 100%;
  }

  .imagen-tipo {
    width: 100%;
    height: 180px;
  }

  .texto-tipo {
    width: 100%;
    padding: 15px;
    padding-bottom: 45px; /* Espacio para la flecha */
    min-height: 200px; /* Altura mínima aumentada */
  }

  .texto-tipo p:last-of-type {
    margin-bottom: 25px; /* Espacio adicional después del último párrafo */
  }
}

/* Escritorios pequeños (992px - 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
  .grid-container-tipos {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }

  .grid-item-tipo {
    flex-direction: row;
    height: 200px;
  }

  .imagen-tipo {
    width: 200px;
    min-width: 200px;
    height: 100%;
  }

  .texto-tipo {
    width: calc(100% - 200px);
    padding: 20px;
  }

  .texto-tipo h2 {
    font-size: 1.2rem;
  }

  .texto-tipo p {
    font-size: 0.9rem;
  }
}

/* Escritorios medianos (1200px - 1399px) */
@media (min-width: 1200px) and (max-width: 1399px) {
  .grid-container-tipos {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }

  .grid-item-tipo {
    flex-direction: row;
    height: 200px;
  }

  .imagen-tipo {
    width: 200px;
    min-width: 200px;
    height: 100%;
  }

  .texto-tipo {
    width: calc(100% - 200px);
    padding: 20px;
  }

  .texto-tipo h2 {
    font-size: 1.2rem;
  }

  .texto-tipo p {
    font-size: 0.9rem;
  }
}

/* Escritorios grandes (1400px - 1999px) */
@media (min-width: 1400px) and (max-width: 1999px) {
  .cuarta-pantalla {
    max-width: 1400px;
  }

  .grid-container-tipos {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }

  .grid-item-tipo {
    flex-direction: row;
    height: 220px;
  }

  .imagen-tipo {
    width: 220px;
    min-width: 220px;
    height: 100%;
  }

  .texto-tipo {
    width: calc(100% - 220px);
    padding: 25px;
  }

  .texto-tipo h2 {
    font-size: 1.3rem;
  }

  .texto-tipo p {
    font-size: 1rem;
  }
}

/* Pantallas ultra anchas (2000px y superior) */
@media (min-width: 2000px) {
  .cuarta-pantalla {
    max-width: 1800px;
  }

  .grid-container-tipos {
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
    max-width: 1800px;
    margin: 0 auto;
  }

  .grid-item-tipo {
    height: 250px;
  }

  .imagen-tipo {
    width: 250px;
    min-width: 250px;
  }

  .texto-tipo {
    width: calc(100% - 250px);
    padding: 30px;
  }

  .texto-tipo h2 {
    font-size: 1.6rem;
  }

  .texto-tipo p {
    font-size: 1.2rem;
  }

  .flecha img {
    width: 30px;
    height: 30px;
  }
}
