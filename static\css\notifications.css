.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.notification {
    position: relative;
    padding: 20px;
    margin-bottom: 15px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    min-width: 300px;
    max-width: 400px;
    animation: slideIn 0.5s ease-out;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.notification.success {
    background-color: rgba(40, 167, 69, 0.9);
    border-left: 4px solid #28a745;
}

.notification.error {
    background-color: rgba(220, 53, 69, 0.9);
    border-left: 4px solid #dc3545;
}

.notification.warning {
    background-color: rgba(255, 193, 7, 0.9);
    border-left: 4px solid #ffc107;
}

.notification.info {
    background-color: rgba(23, 162, 184, 0.9);
    border-left: 4px solid #17a2b8;
}

.notification-icon {
    font-size: 24px;
    margin-right: 15px;
    color: white;
}

.notification-content {
    flex: 1;
    color: white;
}

.notification-title {
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 1.1em;
}

.notification-message {
    font-size: 0.9em;
    opacity: 0.9;
}

.notification-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 16px;
    opacity: 0.7;
    transition: opacity 0.3s;
}

.notification-close:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.notification.slide-out {
    animation: slideOut 0.5s ease-in forwards;
}

/* Estilos para el modal de notificación */
.modal-notification {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.modal-notification-content {
    background-color: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    animation: modalFadeIn 0.3s ease-out;
}

.modal-notification-icon {
    font-size: 60px;
    margin-bottom: 20px;
    color: #28a745;
}

.modal-notification-title {
    font-size: 1.5em;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
}

.modal-notification-message {
    font-size: 1.1em;
    color: #666;
    margin-bottom: 25px;
}

.modal-notification-button {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 10px 25px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.3s;
}

.modal-notification-button:hover {
    background-color: #218838;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Overlay para el fondo oscuro */
.notification-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    z-index: 999;
    opacity: 0;
    transition: opacity 0.3s ease;
    backdrop-filter: blur(5px);
}

.notification-overlay.show {
    opacity: 1;
}

/* Animaciones */
@keyframes bounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Efecto de brillo */
.notification::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    border-radius: 15px;
    z-index: -1;
    animation: shine 3s infinite;
}

@keyframes shine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Efecto de pulso para el ícono */
.notification i {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Responsive */
@media (max-width: 480px) {
    .notification {
        min-width: 90%;
        padding: 20px;
    }
    
    .notification p {
        font-size: 20px;
    }
    
    .notification i {
        font-size: 36px;
    }
} 