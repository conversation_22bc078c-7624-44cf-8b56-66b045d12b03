<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Menú - VerdeQR</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/sidebar.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/avatars.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body class="gestion-body">
    <!-- Menú lateral -->
    <header class="header-principal">
        <div class="logo-container">
            <img src="{{ url_for('static', filename='css/js/img/logo.png') }}" alt="Logo VerdeQR" class="logo" style="border-radius: 50%; width: 60px; height: 60px; object-fit: cover;">
        </div>

        <nav class="menu-principal">
            <ul id="menu">
                <li><a href="{{ url_for('gestion') }}"><i class="fas fa-home"></i> Inicio</a></li>
                <li><a href="{{ url_for('arbol') }}"><i class="fas fa-tree"></i> Árbol</a></li>
                <li><a href="{{ url_for('centro') }}"><i class="fas fa-school"></i> Centros</a></li>
                <li><a href="{{ url_for('especie') }}"><i class="fas fa-leaf"></i> Especies</a></li>
                <li><a href="{{ url_for('uso_arbol') }}"><i class="fas fa-recycle"></i> Usos de Árbol</a></li>
                <li><a href="{{ url_for('tipo_bosque') }}"><i class="fas fa-forest"></i> Tipos de Bosque</a></li>
                <li><a href="{{ url_for('medidas_arbol') }}"><i class="fas fa-ruler-combined"></i> Medidas</a></li>
                <li><a href="{{ url_for('qr') }}"><i class="fas fa-qrcode"></i> QR</a></li>
                <li><a href="{{ url_for('sugerencias') }}"><i class="fas fa-comment"></i> Sugerencias</a></li>
            </ul>
        </nav>

        <div class="user-section">
            <a href="{{ url_for('perfil') }}" class="user-profile-link">
                {% if session['usuario'].get('Imagen') %}
                    <img src="{{ url_for('static', filename=session['usuario']['Imagen']) }}" alt="Usuario" class="user-avatar">
                {% else %}
                    {% if determinar_genero(session['usuario']['Nombres']) == 'femenino' %}
                        <img src="{{ url_for('static', filename='css/js/img/avatarf.jpg') }}" alt="Usuario" class="user-avatar">
                    {% else %}
                        <img src="{{ url_for('static', filename='css/js/img/avatarm.jpg') }}" alt="Usuario" class="user-avatar">
                    {% endif %}
                {% endif %}
                <div class="user-info">
                    <span class="user-name">{{ session['usuario']['Nombres'] }} {{ session['usuario']['Apellidos'] }}</span>
                    <span class="user-email">{{ session['usuario']['Correo'] }}</span>
                </div>
            </a>
        </div>
    </header>
</body>
</html>