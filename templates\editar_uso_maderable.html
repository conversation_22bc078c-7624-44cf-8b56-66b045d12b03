<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Detalles de Uso Maderable - VerdeQR</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/gestion.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/common_header.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/buttons.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/notifications.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/responsive_large.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="gestion-body">
    {% include 'flash_messages.html' %}
    <header class="header-principal">
        <div class="logo-container">
            <img src="{{ url_for('static', filename='css/js/img/logo.png') }}" alt="Logo VerdeQR" class="logo" style="border-radius: 50%; width: 60px; height: 60px; object-fit: cover;">
        </div>
        <nav class="menu-principal">
            <ul id="menu">
                <li><a href="{{ url_for('gestion') }}"><i class="fas fa-home"></i><span>Inicio</span></a></li>
                <li><a href="{{ url_for('arbol') }}"><i class="fas fa-tree"></i><span>Árboles</span></a></li>
                <li><a href="{{ url_for('centro') }}"><i class="fas fa-university"></i><span>Centros</span></a></li>
                <li><a href="{{ url_for('especie') }}"><i class="fas fa-leaf"></i><span>Especies</span></a></li>
                <li><a href="{{ url_for('uso_arbol') }}" class="active"><i class="fas fa-tags"></i><span>Usos</span></a></li>
                <li><a href="{{ url_for('tipo_bosque') }}"><i class="fas fa-mountain"></i><span>Tipos de Bosque</span></a></li>
                <li><a href="{{ url_for('curiosidades') }}"><i class="fas fa-lightbulb"></i><span>Curiosidades</span></a></li>
                <li><a href="{{ url_for('interacciones') }}"><i class="fas fa-network-wired"></i><span>Interacciones Ecológicas</span></a></li>
                <li><a href="{{ url_for('qr') }}"><i class="fas fa-qrcode"></i><span>QR</span></a></li>
                <li><a href="{{ url_for('sugerencias') }}"><i class="fas fa-comments"></i><span>Sugerencias</span></a></li>
                <li><a href="{{ url_for('gestion_usuarios') }}"><i class="fas fa-users"></i><span>Usuarios</span></a></li>
            </ul>
        </nav>
        <div class="user-section">
            <a href="{{ url_for('perfil') }}" class="user-profile-link">
                {% if session['usuario'].get('Imagen') %}
                    <img src="{{ url_for('static', filename=session['usuario']['Imagen']) }}" alt="Usuario" class="user-avatar">
                {% else %}
                    {% if determinar_genero(session['usuario']['Nombres']) == 'femenino' %}
                        <img src="{{ url_for('static', filename='css/js/img/avatarf.jpg') }}" alt="Usuario" class="user-avatar">
                    {% else %}
                        <img src="{{ url_for('static', filename='css/js/img/avatarm.jpg') }}" alt="Usuario" class="user-avatar">
                    {% endif %}
                {% endif %}
                <div class="user-info">
                    <span class="user-name">{{ session['usuario']['Nombre'] }}</span>
                    <span class="user-email">{{ session['usuario']['Correo'] }}</span>
                </div>
            </a>
        </div>
    </header>

    <main class="container">
        <div class="contenedor-seccion">
            <h2>Detalles de Uso Maderable</h2>
            <div class="info-uso">
                <p><strong>Especie:</strong> {{ uso.EspecieNombre if uso.EspecieNombre else 'No especificada' }}</p>
                <p><strong>Nombre del Uso:</strong> {{ uso.Nombre }}</p>
            </div>
            
            <form method="POST" action="{{ url_for('editar_uso_maderable', id=uso.IDUso) }}" class="formulario-gestion">
                <div class="form-group">
                    <label for="dureza">Dureza:</label>
                    <select id="dureza" name="dureza" class="form-control" required>
                        <option value="Blanda" {% if uso_maderable and uso_maderable.Dureza == 'Blanda' %}selected{% endif %}>Blanda</option>
                        <option value="Media" {% if uso_maderable and uso_maderable.Dureza == 'Media' %}selected{% endif %}>Media</option>
                        <option value="Dura" {% if uso_maderable and uso_maderable.Dureza == 'Dura' %}selected{% endif %}>Dura</option>
                        <option value="Muy Dura" {% if uso_maderable and uso_maderable.Dureza == 'Muy Dura' %}selected{% endif %}>Muy Dura</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="resistencia">Resistencia:</label>
                    <select id="resistencia" name="resistencia" class="form-control" required>
                        <option value="Baja" {% if uso_maderable and uso_maderable.Resistencia == 'Baja' %}selected{% endif %}>Baja</option>
                        <option value="Media" {% if uso_maderable and uso_maderable.Resistencia == 'Media' %}selected{% endif %}>Media</option>
                        <option value="Alta" {% if uso_maderable and uso_maderable.Resistencia == 'Alta' %}selected{% endif %}>Alta</option>
                        <option value="Muy Alta" {% if uso_maderable and uso_maderable.Resistencia == 'Muy Alta' %}selected{% endif %}>Muy Alta</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="uso_final">Uso Final:</label>
                    <input type="text" id="uso_final" name="uso_final" class="form-control" value="{{ uso_maderable.UsoFinal if uso_maderable else '' }}" placeholder="Ej: Construcción, Muebles, etc." required>
                </div>
                
                <div class="form-buttons">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Guardar Cambios
                    </button>
                    <a href="{{ url_for('uso_arbol') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Volver
                    </a>
                </div>
            </form>
        </div>
    </main>

    <footer class="footer">
        <div class="footer-content">
            <div class="footer-section">
                <h3>VerdeQR</h3>
                <p>Sistema de gestión de árboles y códigos QR</p>
            </div>
            <div class="footer-section">
                <h3>Enlaces</h3>
                <ul>
                    <li><a href="{{ url_for('principal') }}">Inicio</a></li>
                    <li><a href="{{ url_for('contacto') }}">Contacto</a></li>
                    <li><a href="{{ url_for('acerca_de') }}">Acerca de</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>Contacto</h3>
                <p>Email: <EMAIL></p>
                <p>Teléfono: +************</p>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2023 VerdeQR. Todos los derechos reservados.</p>
        </div>
    </footer>
</body>
</html>
